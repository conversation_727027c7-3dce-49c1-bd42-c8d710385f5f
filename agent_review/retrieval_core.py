import os
import warnings

import urllib3
from dotenv import load_dotenv
from haystack import Document
from haystack.components.embedders import OpenAIDocumentEmbedder, OpenAITextEmbedder
from haystack.document_stores.types import DuplicatePolicy
from haystack.utils import Secret
from haystack_integrations.components.retrievers.opensearch import (
    OpenSearchHybridRetriever,
)
from haystack_integrations.document_stores.opensearch import OpenSearchDocumentStore

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings("ignore", message=".*verify_certs=False.*")

load_dotenv(override=True)


class RetrievalCore:
    def __init__(self):
        self.doc_store = OpenSearchDocumentStore(
            hosts=f"https://{os.environ['OPENSEARCH_HOST']}",
            http_auth=(
                os.environ["OPENSEARCH_USERNAME"],
                os.environ["OPENSEARCH_PASSWORD"],
            ),
            index=os.environ["OPENSEARCH_INDEX"],
            embedding_dim=os.environ["EMBEDDING_DIMS"],
            verify_certs=False,
            use_ssl=True,
        )

        self.embed_config = {
            "api_base_url": os.environ["EMBEDDING_BASE_URL"],
            "api_key": Secret.from_token(os.environ["EMBEDDING_API_KEY"]),
            "model": os.environ["EMBEDDING_MODEL"],
            "http_client_kwargs": {"verify": False, "timeout": 30.0},
        }

        self.doc_embedder = OpenAIDocumentEmbedder(**self.embed_config)
        self.text_embedder = OpenAITextEmbedder(**self.embed_config)

        self.retriever = OpenSearchHybridRetriever(
            document_store=self.doc_store,
            embedder=self.text_embedder,
            top_k_bm25=3,
            top_k_embedding=3,
            join_mode="reciprocal_rank_fusion",
        )

    def insert_documents(self, documents: list[dict], content_key: str = "msg"):
        docs_list = [
            Document(
                content=d[content_key],
                meta={k: v for k, v in d.items() if k != content_key},
            )
            for d in documents
        ]
        docs = self.doc_embedder.run(documents=docs_list)
        self.doc_store.write_documents(docs["documents"], policy=DuplicatePolicy.SKIP)

    def search(self, query: str):
        results = self.retriever.run(
            query=query, filters_bm25=None, filters_embedding=None
        )
        return results["documents"]

    def delete_all_documents(self):
        documents = self.doc_store.filter_documents()
        # print(documents)
        for doc in documents:
            self.doc_store.delete_documents([doc.id])


if __name__ == "__main__":
    docs = [
        Document(content="Machine learning is a subset of artificial intelligence."),
        Document(content="Deep learning is a subset of machine learning."),
        Document(content="Natural language processing is a field of AI."),
        Document(content="Reinforcement learning is a type of machine learning."),
        Document(content="Supervised learning is a type of machine learning."),
    ]

    knowledge_base = RetrievalCore()
    knowledge_base.insert_documents(docs)
    print(knowledge_base.search("What is reinforcement learning?"))

    # knowledge_base.delete_all_documents()
    print(knowledge_base.doc_store.count_documents())
