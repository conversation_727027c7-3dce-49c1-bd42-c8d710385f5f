import os

import httpx
from dotenv import load_dotenv
from smolagents import CodeAgent, OpenAIServerModel

from agent_review.retrieval_core import RetrievalCore
from agent_review.retrieval_tool import retriever_tool

load_dotenv(override=True)


class AgenticRAG:
    def __init__(self):
        self.knowledge_base = RetrievalCore()

        self.agent = CodeAgent(
            tools=[retriever_tool],
            model=OpenAIServerModel(
                model_id=os.environ["MODEL_NAME"],
                api_base=os.environ["QWQ_BASE_URL"],
                api_key=os.environ["QWQ_API_KEY"],
                client_kwargs={"http_client": httpx.Client(verify=False, timeout=60.0)},
            ),
            max_steps=4,
            verbosity_level=2,
        )

    def insert_data(self, caseId: str, messages: list[dict]):
        """Insert messages into knowledge base with case ID.

        Args:
            caseId: The case identifier
            messages: List of message dictionaries containing 'msg' field
        """
        # Add caseId to each message in a list comprehension
        documents = [{**msg, "caseId": caseId} for msg in messages]

        # Insert documents directly
        self.knowledge_base.insert_documents(documents=documents, content_key="msg")

    def run(self, question: str) -> str:
        """Run the agent to get an answer."""
        return self.agent.run(question)


if __name__ == "__main__":
    review_agent = AgenticRAG()
    # print(agent.run("What is reinforcement learning?"))
    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]
    review_agent.insert_data(caseId, messages)
    review_agent.run("用户是否有询问公司的地址？")
